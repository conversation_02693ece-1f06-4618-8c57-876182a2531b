// 优化的插画生成服务，基于用户回答生成风格一致的插画

import { getApiKey } from './apiKeyManager';
import { 
  buildImagePrompt, 
  getReferenceImages,
  extractKeyContent
} from './promptTemplates';

/**
 * 调用OpenAI API生成图像
 * @param {string} prompt - 图像生成提示词
 * @param {Array} referenceImages - 参考图像URL数组
 * @returns {Promise<string>} 生成的图像URL
 */
async function generateImage(prompt, referenceImages = []) {
  const apiKey = getApiKey();
  if (!apiKey) {
    throw new Error('API密钥未设置');
  }
  
  try {
    console.log('生成图像的提示词:', prompt);
    console.log('参考图像:', referenceImages);
    
    const requestBody = {
      model: "dall-e-3",
      prompt: prompt,
      n: 1,
      size: "1024x1024",
      quality: "standard",
      style: "natural"
    };
    
    // 如果有参考图像，添加到请求中
    // 注意：实际API可能不直接支持URL作为参考，这里是示意
    if (referenceImages && referenceImages.length > 0) {
      requestBody.reference_images = referenceImages;
    }
    
    const response = await fetch('https://api.openai.com/v1/images/generations', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      body: JSON.stringify(requestBody)
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`API错误: ${errorData.error?.message || '未知错误'}`);
    }
    
    const data = await response.json();
    return data.data[0].url;
  } catch (error) {
    console.error('图像生成失败:', error);
    throw error;
  }
}

/**
 * 保存生成的图像到本地缓存
 * @param {string} imageUrl - 生成的图像URL
 * @param {number} pageId - 页面ID
 * @returns {Promise<string>} 本地图像路径
 */
async function saveGeneratedImage(imageUrl, pageId) {
  try {
    // 在实际应用中，这里会实现将远程图像保存到本地的逻辑
    // 在前端应用中，可以使用localStorage或IndexedDB存储图像URL
    
    // 存储图像URL到localStorage
    const cacheKey = `generated_image_page_${pageId}`;
    localStorage.setItem(cacheKey, imageUrl);
    
    console.log(`保存图像: ${imageUrl} 到页面ID: ${pageId}`);
    
    // 返回图像URL作为本地路径
    return imageUrl;
  } catch (error) {
    console.error('保存图像失败:', error);
    throw error;
  }
}

/**
 * 从缓存中获取已生成的图像
 * @param {number} pageId - 页面ID
 * @returns {string|null} 缓存的图像URL或null
 */
function getCachedImage(pageId) {
  try {
    const cacheKey = `generated_image_page_${pageId}`;
    return localStorage.getItem(cacheKey);
  } catch (error) {
    console.error('获取缓存图像失败:', error);
    return null;
  }
}

/**
 * 主函数：根据用户回答生成插画
 * @param {string} answer - 用户的回答内容
 * @param {number} pageId - 交互页面ID
 * @param {Object} context - 当前故事上下文
 * @param {Array} allImages - 所有可用的图像
 * @returns {Promise<string>} 生成的图像URL或路径
 */
export async function generateIllustrationFromAnswer(answer, pageId, context, allImages) {
  try {
    // 首先检查缓存
    const cachedImage = getCachedImage(pageId);
    if (cachedImage) {
      console.log(`使用缓存的图像: ${cachedImage}`);
      return cachedImage;
    }
    
    // 构建提示词
    const prompt = buildImagePrompt(answer, context, pageId);
    
    // 获取参考图像
    const referenceImages = getReferenceImages(context.currentPageIndex, allImages);
    
    // 生成图像
    const imageUrl = await generateImage(prompt, referenceImages);
    
    // 保存图像
    const savedImagePath = await saveGeneratedImage(imageUrl, pageId);
    
    return savedImagePath;
  } catch (error) {
    console.error('根据回答生成插画失败:', error);
    throw error;
  }
}

/**
 * 检查生成的插画是否与现有风格一致
 * @param {string} generatedImageUrl - 生成的图像URL
 * @param {Array} referenceImages - 参考图像URL数组
 * @returns {Promise<boolean>} 是否风格一致
 */
export async function checkStyleConsistency(generatedImageUrl, referenceImages) {
  // 在实际应用中，这里可以实现风格一致性检查逻辑
  // 可以使用计算机视觉API或简单的颜色分析
  
  // 模拟检查过程
  console.log('检查风格一致性:', generatedImageUrl, referenceImages);
  
  // 默认返回一致
  return true;
}

/**
 * 清除特定页面的图像缓存
 * @param {number} pageId - 页面ID
 */
export function clearImageCache(pageId) {
  try {
    const cacheKey = `generated_image_page_${pageId}`;
    localStorage.removeItem(cacheKey);
    console.log(`已清除页面${pageId}的图像缓存`);
  } catch (error) {
    console.error('清除图像缓存失败:', error);
  }
}

/**
 * 清除所有页面的图像缓存
 */
export function clearAllImageCache() {
  try {
    // 获取所有localStorage键
    const keys = Object.keys(localStorage);
    
    // 筛选出图像缓存键
    const imageCacheKeys = keys.filter(key => key.startsWith('generated_image_page_'));
    
    // 删除所有图像缓存
    imageCacheKeys.forEach(key => localStorage.removeItem(key));
    
    console.log(`已清除所有图像缓存，共${imageCacheKeys.length}项`);
  } catch (error) {
    console.error('清除所有图像缓存失败:', error);
  }
}

export default {
  generateIllustrationFromAnswer,
  checkStyleConsistency,
  clearImageCache,
  clearAllImageCache
};
