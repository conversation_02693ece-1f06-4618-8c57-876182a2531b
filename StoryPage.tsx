// 集成基于用户回答生成插画的功能到StoryPage组件

import { useState, useEffect, useRef } from 'react';
import { generateIllustrationFromAnswer } from '../services/illustrationGenerator';

interface StoryPageProps {
  page: {
    id: number;
    content: string;
    image?: string;
    isInteractive: boolean;
    question?: string;
    guidance?: string;
  };
  onNext: () => void;
  onResponseSubmit: (pageId: number, response: string) => void;
  userResponses?: Array<{pageId: number, response: string}>;
}

export function StoryPage({ 
  page, 
  onNext, 
  onResponseSubmit,
  userResponses = []
}: StoryPageProps) {
  const [userResponse, setUserResponse] = useState('');
  const [responseSubmitted, setResponseSubmitted] = useState(false);
  const [isListening, setIsListening] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [readingComplete, setReadingComplete] = useState(false);
  const [timeLeft, setTimeLeft] = useState(30);
  const [showGuidance, setShowGuidance] = useState(false);
  const timerStartedRef = useRef(false);
  const timerRef = useRef<number | null>(null);
  const [generatingImage, setGeneratingImage] = useState(false);
  const [generatedImageUrl, setGeneratedImageUrl] = useState('');
  const [imageError, setImageError] = useState('');
  
  // 检查是否已有该页面的回答
  useEffect(() => {
    if (page.isInteractive && userResponses) {
      const existingResponse = userResponses.find(r => r.pageId === page.id);
      if (existingResponse) {
        setUserResponse(existingResponse.response);
        setResponseSubmitted(true);
      } else {
        setUserResponse('');
        setResponseSubmitted(false);
      }
    }
  }, [page.id, userResponses]);
  
  // 重置状态
  useEffect(() => {
    setTimeLeft(30);
    setShowGuidance(false);
    timerStartedRef.current = false;
    setGeneratedImageUrl('');
    setImageError('');
    
    // 自动朗读页面内容
    readPageContent();
    
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
      
      // 确保在组件卸载时停止语音
      if ('speechSynthesis' in window) {
        window.speechSynthesis.cancel();
      }
    };
  }, [page.id]);
  
  // 监听朗读完成后开始计时（仅交互页面）
  useEffect(() => {
    if (page.isInteractive && readingComplete && !responseSubmitted && !timerStartedRef.current) {
      timerStartedRef.current = true;
      
      timerRef.current = window.setInterval(() => {
        setTimeLeft(prevTime => {
          if (prevTime <= 1) {
            if (timerRef.current) {
              clearInterval(timerRef.current);
            }
            setShowGuidance(true);
            // 朗读引导提示
            if (page.guidance) {
              speakText(page.guidance);
            }
            return 0;
          }
          return prevTime - 1;
        });
      }, 1000);
    }
    
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [readingComplete, page.isInteractive, responseSubmitted, page.guidance]);
  
  // 朗读页面内容
  const readPageContent = () => {
    if ('speechSynthesis' in window) {
      // 取消之前的语音
      window.speechSynthesis.cancel();
      
      setIsSpeaking(true);
      
      const textToRead = page.isInteractive ? page.question || '' : page.content;
      const utterance = new SpeechSynthesisUtterance(textToRead);
      
      // 设置语音属性
      utterance.lang = 'zh-CN';
      utterance.rate = 0.9;
      utterance.pitch = 1.0;
      
      utterance.onend = () => {
        setIsSpeaking(false);
        setReadingComplete(true);
      };
      
      window.speechSynthesis.speak(utterance);
    }
  };
  
  // 朗读文本
  const speakText = (text: string) => {
    if ('speechSynthesis' in window) {
      // 取消之前的语音
      window.speechSynthesis.cancel();
      
      const utterance = new SpeechSynthesisUtterance(text);
      
      // 设置语音属性
      utterance.lang = 'zh-CN';
      utterance.rate = 0.9;
      utterance.pitch = 1.0;
      
      window.speechSynthesis.speak(utterance);
    }
  };
  
  // 开始语音输入
  const startVoiceInput = () => {
    // 定义SpeechRecognition类型
    interface SpeechRecognitionResult {
      readonly isFinal: boolean;
      readonly length: number;
      [index: number]: {
        readonly confidence: number;
        readonly transcript: string;
      };
    }
    
    interface SpeechRecognitionResultList {
      readonly length: number;
      [index: number]: SpeechRecognitionResult;
    }
    
    interface SpeechRecognitionEvent extends Event {
      readonly resultIndex: number;
      readonly results: SpeechRecognitionResultList;
    }
    
    interface SpeechRecognitionErrorEvent extends Event {
      error: string;
    }
    
    interface SpeechRecognition extends EventTarget {
      lang: string;
      continuous: boolean;
      interimResults: boolean;
      start: () => void;
      stop: () => void;
      onstart: (event: Event) => void;
      onresult: (event: SpeechRecognitionEvent) => void;
      onerror: (event: SpeechRecognitionErrorEvent) => void;
      onend: (event: Event) => void;
    }
    
    // 检查浏览器支持
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
      const SpeechRecognitionConstructor = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition;
      const recognition = new SpeechRecognitionConstructor() as SpeechRecognition;
      
      recognition.lang = 'zh-CN';
      recognition.continuous = false;
      recognition.interimResults = true;
      
      recognition.onstart = () => {
        setIsListening(true);
      };
      
      recognition.onresult = (event: SpeechRecognitionEvent) => {
        let transcript = '';
        // 安全地处理results，避免Array.from类型错误
        for (let i = 0; i < event.results.length; i++) {
          if (event.results[i][0] && event.results[i][0].transcript) {
            transcript += event.results[i][0].transcript;
          }
        }
        
        setUserResponse(transcript);
      };
      
      recognition.onerror = (event: SpeechRecognitionErrorEvent) => {
        console.error('语音识别错误:', event.error);
        setIsListening(false);
      };
      
      recognition.onend = () => {
        setIsListening(false);
      };
      
      recognition.start();
    } else {
      alert('您的浏览器不支持语音识别功能');
    }
  };
  
  // 提交回答
  const handleSubmit = async () => {
    if (userResponse.trim() === '') return;
    
    // 停止计时器
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }
    
    // 提交回答
    onResponseSubmit(page.id, userResponse);
    setResponseSubmitted(true);
    
    // 朗读感谢信息
    speakText('谢谢你的回答！');
    
    // 生成基于回答的插画
    try {
      setGeneratingImage(true);
      setImageError('');
      
      // 获取所有可用的图像
      const allImages = [
        { pageIndex: 0, url: '/assets/images/page1.png' },
        { pageIndex: 1, url: '/assets/images/page2.png' },
        { pageIndex: 2, url: '/assets/images/page3.png' },
        { pageIndex: 4, url: '/assets/images/page5.png' },
        { pageIndex: 5, url: '/assets/images/page6.png' },
        { pageIndex: 6, url: '/assets/images/page7.png' },
        { pageIndex: 8, url: '/assets/images/page9.png' },
        { pageIndex: 9, url: '/assets/images/page10.png' },
        { pageIndex: 11, url: '/assets/images/page12.png' }
      ];
      
      // 当前页面索引
      const currentPageIndex = page.id - 1;
      
      // 当前故事上下文
      const context = {
        currentPageIndex,
        currentPage: page
      };
      
      // 生成插画
      const imageUrl = await generateIllustrationFromAnswer(
        userResponse,
        page.id,
        context,
        allImages
      );
      
      setGeneratedImageUrl(imageUrl);
    } catch (error) {
      console.error('生成插画失败:', error);
      setImageError(`生成插画失败: ${(error as Error).message || '未知错误'}`);
    } finally {
      setGeneratingImage(false);
    }
  };
  
  // 重新生成插画
  const handleRegenerateImage = async () => {
    try {
      setGeneratingImage(true);
      setImageError('');
      
      // 获取所有可用的图像
      const allImages = [
        { pageIndex: 0, url: '/assets/images/page1.png' },
        { pageIndex: 1, url: '/assets/images/page2.png' },
        { pageIndex: 2, url: '/assets/images/page3.png' },
        { pageIndex: 4, url: '/assets/images/page5.png' },
        { pageIndex: 5, url: '/assets/images/page6.png' },
        { pageIndex: 6, url: '/assets/images/page7.png' },
        { pageIndex: 8, url: '/assets/images/page9.png' },
        { pageIndex: 9, url: '/assets/images/page10.png' },
        { pageIndex: 11, url: '/assets/images/page12.png' }
      ];
      
      // 当前页面索引
      const currentPageIndex = page.id - 1;
      
      // 当前故事上下文
      const context = {
        currentPageIndex,
        currentPage: page
      };
      
      // 生成插画
      const imageUrl = await generateIllustrationFromAnswer(
        userResponse,
        page.id,
        context,
        allImages
      );
      
      setGeneratedImageUrl(imageUrl);
    } catch (error) {
      console.error('重新生成插画失败:', error);
      setImageError(`重新生成插画失败: ${(error as Error).message || '未知错误'}`);
    } finally {
      setGeneratingImage(false);
    }
  };
  
  return (
    <div className="bg-white rounded-lg shadow-md p-6 mb-6">
      {/* 页面内容 */}
      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-4">
          {page.isInteractive ? '交互环节' : `第 ${page.id} 页`}
        </h2>
        
        {/* 页面图片 */}
        <div className="mb-4 relative">
          {page.isInteractive && responseSubmitted && generatedImageUrl ? (
            // 显示基于用户回答生成的插画
            <div className="relative">
              <img 
                src={generatedImageUrl} 
                alt={`基于回答生成的插画`} 
                className="w-full h-auto rounded-lg mb-2"
              />
              <div className="text-sm text-gray-500 italic text-center">
                基于你的回答生成的插画
              </div>
              
              {/* 重新生成按钮 */}
              <button
                onClick={handleRegenerateImage}
                disabled={generatingImage}
                className="mt-2 px-3 py-1 bg-blue-100 text-blue-700 rounded-md text-sm hover:bg-blue-200 transition-colors"
              >
                {generatingImage ? '生成中...' : '重新生成插画'}
              </button>
            </div>
          ) : (
            // 显示原始页面图片
            page.image && (
              <img 
                src={page.image} 
                alt={`第 ${page.id} 页插图`} 
                className="w-full h-auto rounded-lg"
              />
            )
          )}
          
          {/* 图片生成错误提示 */}
          {imageError && (
            <div className="mt-2 p-2 bg-red-50 text-red-700 rounded-md text-sm">
              {imageError}
            </div>
          )}
          
          {/* 图片生成加载状态 */}
          {generatingImage && (
            <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-70 rounded-lg">
              <div className="flex flex-col items-center">
                <div className="animate-spin h-8 w-8 border-4 border-blue-500 border-t-transparent rounded-full mb-2"></div>
                <div className="text-blue-600 font-medium">正在生成插画...</div>
              </div>
            </div>
          )}
        </div>
        
        {/* 页面文本内容 */}
        <div className="prose max-w-none">
          {page.isInteractive ? (
            <div>
              <p className="font-medium text-lg mb-4">{page.question}</p>
              
              {/* 显示引导提示 */}
              {showGuidance && !responseSubmitted && (
                <div className="p-4 bg-amber-50 border border-amber-200 rounded-md mb-4">
                  <p className="text-amber-800">{page.guidance}</p>
                </div>
              )}
              
              {/* 显示用户回答 */}
              {responseSubmitted ? (
                <div className="mt-4">
                  <h3 className="font-medium text-gray-700">你的回答:</h3>
                  <p className="p-3 bg-blue-50 rounded-md">{userResponse}</p>
                </div>
              ) : (
                <div className="mt-4">
                  {/* 倒计时 */}
                  <div className="flex justify-between text-sm text-gray-500 mb-1">
                    <span>请在30秒内回答问题</span>
                    <span>{timeLeft}秒</span>
                  </div>
                  
                  {/* 回答输入框 */}
                  <textarea
                    value={userResponse}
                    onChange={(e) => setUserResponse(e.target.value)}
                    placeholder="在这里输入你的回答..."
                    className="w-full p-3 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    rows={4}
                  ></textarea>
                  
                  <div className="flex mt-2 gap-2">
                    {/* 语音输入按钮 */}
                    <button
                      onClick={startVoiceInput}
                      className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors flex items-center"
                    >
                      {isListening ? (
                        <>
                          <span className="mr-2">正在聆听...</span>
                          <span className="flex h-3 w-3">
                            <span className="animate-ping absolute h-3 w-3 rounded-full bg-green-400 opacity-75"></span>
                            <span className="relative rounded-full h-3 w-3 bg-green-500"></span>
                          </span>
                        </>
                      ) : (
                        '开始语音输入'
                      )}
                    </button>
                    
                    {/* 提交按钮 */}
                    <button
                      onClick={handleSubmit}
                      disabled={userResponse.trim() === ''}
                      className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors disabled:bg-gray-400"
                    >
                      提交回答
                    </button>
                  </div>
                </div>
              )}
            </div>
          ) : (
            <p>{page.content}</p>
          )}
        </div>
      </div>
      
      {/* 朗读按钮 */}
      <div className="mb-4">
        <button
          onClick={readPageContent}
          disabled={isSpeaking}
          className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors disabled:bg-gray-400"
        >
          {isSpeaking ? '正在朗读...' : '朗读内容'}
        </button>
      </div>
      
      {/* 继续阅读按钮（非交互页面） */}
      {!page.isInteractive && (
        <button
          onClick={onNext}
          disabled={!readingComplete}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors disabled:bg-gray-400"
        >
          {!readingComplete ? '请等待朗读完成...' : '继续阅读'}
        </button>
      )}
      
      {/* 继续阅读按钮（交互页面，已回答） */}
      {page.isInteractive && responseSubmitted && (
        <button
          onClick={onNext}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
        >
          继续阅读
        </button>
      )}
    </div>
  );
}
