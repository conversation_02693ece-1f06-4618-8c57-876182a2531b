// TypeScript 类型定义文件，用于插画生成服务

/**
 * 故事页面接口
 */
export interface StoryPage {
  id: number;
  content: string;
  image?: string;
  isInteractive: boolean;
  question?: string;
  guidance?: string;
}

/**
 * 故事上下文接口
 */
export interface StoryContext {
  currentPageIndex: number;
  currentPage: StoryPage;
}

/**
 * 图像引用接口
 */
export interface ImageReference {
  pageIndex: number;
  url: string;
}

/**
 * 从用户回答中提取的关键内容接口
 */
export interface ExtractedContent {
  characters: string[];
  actions: string[];
  emotions: string[];
  sceneElements: string[];
  items: string[];
  timeAndWeather: string[];
}

/**
 * 根据用户回答生成插画
 * @param answer - 用户的回答内容
 * @param pageId - 交互页面ID
 * @param context - 当前故事上下文
 * @param allImages - 所有可用的图像
 * @returns 生成的图像URL或路径
 */
export function generateIllustrationFromAnswer(
  answer: string,
  pageId: number,
  context: StoryContext,
  allImages: ImageReference[]
): Promise<string>;

/**
 * 检查生成的插画是否与现有风格一致
 * @param generatedImageUrl - 生成的图像URL
 * @param referenceImages - 参考图像URL数组
 * @returns 是否风格一致
 */
export function checkStyleConsistency(
  generatedImageUrl: string,
  referenceImages: string[]
): Promise<boolean>;

/**
 * 清除特定页面的图像缓存
 * @param pageId - 页面ID
 */
export function clearImageCache(pageId: number): void;

/**
 * 清除所有页面的图像缓存
 */
export function clearAllImageCache(): void;
