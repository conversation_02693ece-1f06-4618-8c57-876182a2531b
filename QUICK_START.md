# 🚀 快速开始指南

## 📦 项目启动

### 1. 安装依赖
```bash
npm install
```

### 2. 启动开发服务器
```bash
npm run dev
```

### 3. 访问应用
打开浏览器访问: http://localhost:5173

## 🎯 新功能使用指南

### 🎨 UI组件升级
- **现代化卡片设计** - 所有内容现在都使用卡片布局
- **状态徽章** - 实时显示页面状态和进度
- **工具提示** - 悬停按钮查看操作说明
- **改进的进度条** - 更直观的阅读进度显示

### 🔧 交互功能
- **语音朗读** - 点击🔊按钮朗读内容
- **语音输入** - 点击🎤按钮开始语音输入
- **个性化插画** - 提交回答后自动生成专属插画
- **智能倒计时** - 30秒倒计时，紧急状态提醒

### 🎪 特殊功能
- **API密钥设置** - 首次使用需设置OpenAI API密钥
- **重新生成插画** - 不满意可重新生成图片
- **评估报告** - 完成所有交互后查看能力评估

## 🛠️ 开发说明

### 新增组件
- `src/components/ui/card.tsx` - 卡片组件
- `src/components/ui/input.tsx` - 输入框组件
- `src/components/ui/textarea.tsx` - 文本域组件
- `src/components/ui/alert.tsx` - 提示组件
- `src/components/ui/badge.tsx` - 徽章组件
- `src/components/ui/skeleton.tsx` - 骨架屏组件
- `src/components/ui/tooltip.tsx` - 工具提示组件

### 主要改进
- ✅ 现代化UI设计
- ✅ 实时状态指示
- ✅ 改进的用户体验
- ✅ 更好的错误处理
- ✅ 无障碍友好设计

## 🎉 享受使用！

项目现已完全升级，为自闭症儿童提供更好的交互式学习体验！
