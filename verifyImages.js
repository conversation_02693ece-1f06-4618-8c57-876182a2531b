// 验证图片文件和故事数据的对应关系
import fs from 'fs';
import path from 'path';

// 故事数据中的图片配置
const storyImageConfig = [
  { id: 1, image: "/page1.png", isInteractive: false },
  { id: 2, image: "/page2.png", isInteractive: false },
  { id: 3, image: "/page3.png", isInteractive: false },
  { id: 4, image: "/page1.png", isInteractive: true },  // 交互页面使用page1图片
  { id: 5, image: "/page5.png", isInteractive: false },
  { id: 6, image: "/page6.png", isInteractive: false },
  { id: 7, image: "/page7.png", isInteractive: false },
  { id: 8, image: "/page2.png", isInteractive: true },  // 交互页面使用page2图片
  { id: 9, image: "/page9.png", isInteractive: false },
  { id: 10, image: "/page10.png", isInteractive: false },
  { id: 11, image: "/page3.png", isInteractive: true }, // 交互页面使用page3图片
  { id: 12, image: "/page12.png", isInteractive: false }
];

// 需要的图片文件
const requiredImages = [
  'page1.png', 'page2.png', 'page3.png', 'page5.png', 
  'page6.png', 'page7.png', 'page9.png', 'page10.png', 'page12.png'
];

console.log('=== 验证绘本图片配置 ===\n');

// 检查public目录中的图片文件
console.log('📁 检查public目录中的图片文件:');
const publicDir = './public';

if (!fs.existsSync(publicDir)) {
  console.log('❌ public目录不存在');
  process.exit(1);
}

let allFilesExist = true;
let imageStats = [];

for (const imageName of requiredImages) {
  const imagePath = path.join(publicDir, imageName);
  
  if (fs.existsSync(imagePath)) {
    try {
      const stats = fs.statSync(imagePath);
      const sizeKB = (stats.size / 1024).toFixed(2);
      console.log(`✅ ${imageName} - ${sizeKB} KB`);
      imageStats.push({
        name: imageName,
        exists: true,
        size: stats.size,
        sizeKB: sizeKB
      });
    } catch (error) {
      console.log(`⚠️  ${imageName} - 文件存在但无法读取: ${error.message}`);
      allFilesExist = false;
    }
  } else {
    console.log(`❌ ${imageName} - 文件不存在`);
    allFilesExist = false;
    imageStats.push({
      name: imageName,
      exists: false
    });
  }
}

console.log('\n📋 故事页面与图片对应关系:');
storyImageConfig.forEach(page => {
  const imageFile = page.image.replace('/', '');
  const exists = imageStats.find(img => img.name === imageFile)?.exists || false;
  const status = exists ? '✅' : '❌';
  const pageType = page.isInteractive ? '(交互页面)' : '(故事页面)';
  
  console.log(`${status} 第${page.id}页 ${pageType} -> ${page.image}`);
});

console.log('\n📊 总结:');
console.log(`- 需要的图片文件: ${requiredImages.length}`);
console.log(`- 存在的图片文件: ${imageStats.filter(img => img.exists).length}`);
console.log(`- 缺失的图片文件: ${imageStats.filter(img => !img.exists).length}`);

if (allFilesExist) {
  console.log('\n🎉 所有图片文件都已正确配置！');
  console.log('\n📖 绘本页面图片分配:');
  console.log('   非交互页面: 1, 2, 3, 5, 6, 7, 9, 10, 12 (各自对应的图片)');
  console.log('   交互页面: 4(使用page1), 8(使用page2), 11(使用page3)');
} else {
  console.log('\n⚠️  存在缺失的图片文件，请检查上述列表');
}

// 检查是否有额外的图片文件
console.log('\n🔍 检查是否有额外的图片文件:');
try {
  const allFiles = fs.readdirSync(publicDir);
  const pngFiles = allFiles.filter(file => file.toLowerCase().endsWith('.png'));
  const extraFiles = pngFiles.filter(file => !requiredImages.includes(file));
  
  if (extraFiles.length > 0) {
    console.log('📄 发现额外的图片文件:');
    extraFiles.forEach(file => {
      const filePath = path.join(publicDir, file);
      const stats = fs.statSync(filePath);
      const sizeKB = (stats.size / 1024).toFixed(2);
      console.log(`   - ${file} (${sizeKB} KB)`);
    });
  } else {
    console.log('✅ 没有额外的图片文件');
  }
} catch (error) {
  console.log('❌ 无法读取public目录:', error.message);
}
