// 基于用户回答生成插画的核心逻辑实现
// 使用LIBLIB AI平台进行图片生成

import liblibService from './liblibService.js';

// 增强的风格描述 - 专为自闭症儿童设计
const STYLE_DESCRIPTION = `
温暖友好的儿童插画，专为自闭症儿童设计，使用柔和的色彩和简单清晰的形状，角色表情丰富且易于理解。
水彩画风格，轮廓线条清晰且一致，色彩饱和度适中。主要使用温暖的棕色、绿色、蓝色和黄色，避免过于刺激的鲜艳色彩。
柔和的光影效果，避免强烈对比和复杂阴影，确保视觉舒适。轻微的水彩纹理，保持整体平滑感。
场景设计简洁不复杂，背景元素适量且有序排列，主体突出，避免过多分散注意力的细节。
保持与故事其他插图的一致风格和色调，确保整体视觉连贯性。
`;

// 增强的角色描述 - 增加了更多细节和一致性描述
const CHARACTER_DESCRIPTION = `
小熊波波：棕色毛发的小熊，圆脸，大眼睛，友好的表情。穿着红色上衣，蓝色短裤。身高约为画面高度的1/3。
小兔莉莉：灰白色的兔子，长耳朵，温柔的表情。穿着粉色连衣裙，有时戴着花朵发饰。身高略小于波波。
乌龟老师：绿色的乌龟，深棕色的壳，戴着圆形眼镜，智慧的表情。通常拿着一本书或教具。
松鼠兄弟：红棕色的松鼠，蓬松的尾巴，活泼的表情。一个穿黄色衣服，一个穿绿色衣服，以区分。
森林背景：温暖的绿色树木，点缀着黄色和橙色的花朵，蓝色的小溪，棕色的小路。天空通常是淡蓝色。
`;

// 情感表达指南 - 帮助生成更适合自闭症儿童理解的情感表达
const EMOTION_EXPRESSION_GUIDE = `
情感表达应清晰且易于识别：
- 开心/高兴：明显的微笑，眼睛弯曲成弧形
- 悲伤：嘴角下垂，眉毛向上倾斜
- 惊讶：圆形嘴巴，睁大的眼睛
- 害怕：缩小的身体姿势，略微后倾
- 友好：微笑并伸出手/爪子
- 好奇：略微倾斜的头部，专注的眼神

避免复杂或模糊的情感表达，确保情感状态一目了然。
`;

// 交互场景指南 - 针对交互页面的特殊处理
const INTERACTIVE_SCENE_GUIDE = `
交互场景应展现开放性和包容性：
- 角色之间保持适当距离，展示积极互动
- 场景应有邀请性，预留空间让想象中的新角色或元素加入
- 可以包含与用户回答相关的新元素，但保持与故事世界观的一致性
- 避免过于拥挤或混乱的场景，保持视觉清晰度
`;

/**
 * 从用户回答中提取关键内容
 * @param {string} answer - 用户的回答内容
 * @returns {Object} 提取的关键内容
 */
function extractKeyContent(answer) {
  // 提取角色 - 扩展了更多可能的角色名称变体
  const characterPatterns = [
    { names: ['波波', '小熊波波', '小熊'], character: '小熊波波' },
    { names: ['莉莉', '小兔莉莉', '小兔'], character: '小兔莉莉' },
    { names: ['乌龟', '乌龟老师', '老师'], character: '乌龟老师' },
    { names: ['松鼠', '松鼠兄弟'], character: '松鼠兄弟' }
  ];

  const characters = [];
  characterPatterns.forEach(pattern => {
    if (pattern.names.some(name => answer.includes(name))) {
      characters.push(pattern.character);
    }
  });

  // 提取动作和场景 - 扩展了更多关键词
  const actions = [];
  const actionKeywords = [
    '打招呼', '分享', '帮助', '玩', '说话', '微笑', '拥抱', '交朋友',
    '给予', '接受', '学习', '教导', '聆听', '阅读', '画画', '唱歌',
    '跳舞', '跑步', '散步', '探索', '发现', '思考', '想象'
  ];
  actionKeywords.forEach(action => {
    if (answer.includes(action)) actions.push(action);
  });

  // 提取情感 - 扩展了更多情感词汇
  const emotions = [];
  const emotionKeywords = [
    '开心', '高兴', '害怕', '紧张', '兴奋', '好奇', '担心', '勇敢', '友好',
    '快乐', '满足', '惊讶', '惊喜', '感激', '温暖', '安心', '自信', '骄傲',
    '期待', '专注', '平静', '满意'
  ];
  emotionKeywords.forEach(emotion => {
    if (answer.includes(emotion)) emotions.push(emotion);
  });

  // 提取场景元素 - 扩展了更多场景元素
  const sceneElements = [];
  const sceneKeywords = [
    '森林', '树', '花', '草地', '河流', '木屋', '阳光', '雨', '野餐',
    '学校', '教室', '操场', '图书馆', '桥', '山', '洞穴', '池塘', '瀑布',
    '彩虹', '云', '星星', '月亮', '沙滩', '海', '船', '帐篷', '篝火'
  ];
  sceneKeywords.forEach(element => {
    if (answer.includes(element)) sceneElements.push(element);
  });

  // 提取物品 - 新增物品提取
  const items = [];
  const itemKeywords = [
    '书', '玩具', '食物', '水果', '蛋糕', '礼物', '气球', '画', '信',
    '背包', '帽子', '伞', '望远镜', '地图', '笔', '纸', '乐器', '球',
    '风筝', '船', '车', '自行车', '灯笼', '花束', '相机'
  ];
  itemKeywords.forEach(item => {
    if (answer.includes(item)) items.push(item);
  });

  // 提取时间和天气 - 新增时间和天气提取
  const timeAndWeather = [];
  const timeWeatherKeywords = [
    '早晨', '中午', '下午', '傍晚', '晚上', '夜晚',
    '晴天', '雨天', '多云', '雾', '雪', '风', '彩虹'
  ];
  timeWeatherKeywords.forEach(tw => {
    if (answer.includes(tw)) timeAndWeather.push(tw);
  });

  // 如果没有提取到足够的内容，提供默认值
  return {
    characters: characters.length > 0 ? characters : ['小熊波波', '小兔莉莉'],
    actions: actions.length > 0 ? actions : ['微笑', '交朋友'],
    emotions: emotions.length > 0 ? emotions : ['友好', '开心'],
    sceneElements: sceneElements.length > 0 ? sceneElements : ['森林', '阳光'],
    items: items.length > 0 ? items : [],
    timeAndWeather: timeAndWeather.length > 0 ? timeAndWeather : ['白天']
  };
}

/**
 * 增强版：构建图像生成提示词
 * @param {string} answer - 用户的回答内容
 * @param {Object} context - 当前故事上下文
 * @param {number} pageId - 页面ID
 * @returns {string} 完整的提示词
 */
function buildImagePrompt(answer, context, pageId) {
  const keyContent = extractKeyContent(answer);

  // 构建场景描述
  let sceneDescription = `${keyContent.characters.join('和')}在${keyContent.sceneElements.join('和')}中`;

  // 添加时间和天气描述
  if (keyContent.timeAndWeather.length > 0) {
    sceneDescription += `的${keyContent.timeAndWeather.join('和')}`;
  }

  // 添加动作描述
  if (keyContent.actions.length > 0) {
    sceneDescription += `正在${keyContent.actions.join('和')}`;
  }

  // 添加物品描述
  if (keyContent.items.length > 0) {
    sceneDescription += `，周围有${keyContent.items.join('和')}`;
  }

  // 添加情感描述
  if (keyContent.emotions.length > 0) {
    sceneDescription += `，表情${keyContent.emotions.join('和')}`;
  }

  // 构建基础提示词
  let promptBase = `为自闭症儿童绘本创建一幅插图，描绘：${sceneDescription}。
这是基于儿童回答的交互式绘本第${pageId}页插图。

具体情境：${answer}

${CHARACTER_DESCRIPTION}

${EMOTION_EXPRESSION_GUIDE}

${INTERACTIVE_SCENE_GUIDE}

${STYLE_DESCRIPTION}`;

  // 添加上下文相关信息
  if (context && context.currentPage) {
    promptBase += `\n\n当前故事情境：${context.currentPage.content || context.currentPage.question}`;

    // 如果是交互页面，添加问题信息
    if (context.currentPage.isInteractive && context.currentPage.question) {
      promptBase += `\n\n交互问题：${context.currentPage.question}`;
    }
  }

  // 添加风格一致性强调
  promptBase += `\n\n重要：请确保插图风格与绘本其他页面保持一致，使用相同的艺术风格、色彩方案和角色设计。插图应该看起来像是同一位艺术家创作的同一本书的一部分。`;

  return promptBase;
}

/**
 * 增强版：获取参考图像URL
 * @param {number} currentPageIndex - 当前页面索引
 * @param {Array} allImages - 所有可用的图像URL
 * @returns {Array} 参考图像URL数组
 */
function getReferenceImages(currentPageIndex, allImages) {
  const references = [];

  // 添加前一页的图像作为参考（如果存在）
  const prevImages = allImages.filter(img => img.pageIndex < currentPageIndex)
    .sort((a, b) => b.pageIndex - a.pageIndex);

  if (prevImages.length > 0) {
    references.push(prevImages[0].url);

    // 添加第二个前置图像（如果存在）
    if (prevImages.length > 1) {
      references.push(prevImages[1].url);
    }
  }

  // 添加后一页的图像作为参考（如果存在）
  const nextImages = allImages.filter(img => img.pageIndex > currentPageIndex)
    .sort((a, b) => a.pageIndex - b.pageIndex);

  if (nextImages.length > 0) {
    references.push(nextImages[0].url);
  }

  // 如果没有找到足够的相邻页面图像，添加其他可用图像
  if (references.length < 2 && allImages.length > 0) {
    // 按页面索引排序
    const sortedImages = [...allImages].sort((a, b) => a.pageIndex - b.pageIndex);

    // 添加尚未包含的图像
    for (const img of sortedImages) {
      if (!references.includes(img.url) && references.length < 3) {
        references.push(img.url);
      }

      if (references.length >= 3) break;
    }
  }

  return references;
}

/**
 * 调用LIBLIB API生成图像
 * @param {string} prompt - 图像生成提示词
 * @param {Array} referenceImages - 参考图像URL数组（用于在提示词中描述风格）
 * @returns {Promise<string>} 生成的图像URL
 */
async function generateImage(prompt, referenceImages = []) {
  try {
    console.log('使用LIBLIB平台生成图像...');
    console.log('生成图像的提示词:', prompt);
    console.log('参考图像（用于风格描述）:', referenceImages);

    // 增强提示词以保持风格一致性
    let enhancedPrompt = prompt;

    if (referenceImages && referenceImages.length > 0) {
      enhancedPrompt += `\n\n风格一致性要求：请确保生成的插画与现有绘本页面保持完全一致的艺术风格，包括：
- 相同的水彩画技法和纹理
- 一致的色彩饱和度和色调
- 相同的线条粗细和风格
- 统一的角色设计和比例
- 相似的光影处理方式
请特别注意保持与绘本其他页面的视觉连贯性。`;
    }

    // 使用LIBLIB服务生成图像
    return await liblibService.generateImage(enhancedPrompt, '6-8岁');
  } catch (error) {
    console.error('LIBLIB图像生成失败:', error);
    throw error;
  }
}

/**
 * 保存生成的图像到本地缓存
 * @param {string} imageUrl - 生成的图像URL
 * @param {number} pageId - 页面ID
 * @returns {Promise<string>} 本地图像路径
 */
async function saveGeneratedImage(imageUrl, pageId) {
  try {
    // 在实际应用中，这里会实现将远程图像保存到本地的逻辑
    // 在前端应用中，可以使用localStorage或IndexedDB存储图像URL

    // 存储图像URL到localStorage
    const cacheKey = `generated_image_page_${pageId}`;
    localStorage.setItem(cacheKey, imageUrl);

    console.log(`保存图像: ${imageUrl} 到页面ID: ${pageId}`);

    // 返回图像URL作为本地路径
    return imageUrl;
  } catch (error) {
    console.error('保存图像失败:', error);
    throw error;
  }
}

/**
 * 从缓存中获取已生成的图像
 * @param {number} pageId - 页面ID
 * @returns {string|null} 缓存的图像URL或null
 */
function getCachedImage(pageId) {
  try {
    const cacheKey = `generated_image_page_${pageId}`;
    return localStorage.getItem(cacheKey);
  } catch (error) {
    console.error('获取缓存图像失败:', error);
    return null;
  }
}

/**
 * 主函数：根据用户回答生成插画
 * @param {string} answer - 用户的回答内容
 * @param {number} pageId - 交互页面ID
 * @param {Object} context - 当前故事上下文
 * @param {Array} allImages - 所有可用的图像
 * @returns {Promise<string>} 生成的图像URL或路径
 */
export async function generateIllustrationFromAnswer(answer, pageId, context, allImages) {
  try {
    // 首先检查缓存
    const cachedImage = getCachedImage(pageId);
    if (cachedImage) {
      console.log(`使用缓存的图像: ${cachedImage}`);
      return cachedImage;
    }

    // 构建提示词
    const prompt = buildImagePrompt(answer, context, pageId);

    // 获取参考图像
    const referenceImages = getReferenceImages(context.currentPageIndex, allImages);

    // 生成图像
    const imageUrl = await generateImage(prompt, referenceImages);

    // 保存图像
    const savedImagePath = await saveGeneratedImage(imageUrl, pageId);

    return savedImagePath;
  } catch (error) {
    console.error('根据回答生成插画失败:', error);
    throw error;
  }
}

/**
 * 检查生成的插画是否与现有风格一致
 * @param {string} generatedImageUrl - 生成的图像URL
 * @param {Array} referenceImages - 参考图像URL数组
 * @returns {Promise<boolean>} 是否风格一致
 */
export async function checkStyleConsistency(generatedImageUrl, referenceImages) {
  // 在实际应用中，这里可以实现风格一致性检查逻辑
  // 可以使用计算机视觉API或简单的颜色分析

  // 模拟检查过程
  console.log('检查风格一致性:', generatedImageUrl, referenceImages);

  // 默认返回一致
  return true;
}

/**
 * 清除特定页面的图像缓存
 * @param {number} pageId - 页面ID
 */
export function clearImageCache(pageId) {
  try {
    const cacheKey = `generated_image_page_${pageId}`;
    localStorage.removeItem(cacheKey);
    console.log(`已清除页面${pageId}的图像缓存`);
  } catch (error) {
    console.error('清除图像缓存失败:', error);
  }
}

/**
 * 清除所有页面的图像缓存
 */
export function clearAllImageCache() {
  try {
    // 获取所有localStorage键
    const keys = Object.keys(localStorage);

    // 筛选出图像缓存键
    const imageCacheKeys = keys.filter(key => key.startsWith('generated_image_page_'));

    // 删除所有图像缓存
    imageCacheKeys.forEach(key => localStorage.removeItem(key));

    console.log(`已清除所有图像缓存，共${imageCacheKeys.length}项`);
  } catch (error) {
    console.error('清除所有图像缓存失败:', error);
  }
}

export default {
  generateIllustrationFromAnswer,
  checkStyleConsistency,
  clearImageCache,
  clearAllImageCache,
  buildImagePrompt,
  extractKeyContent
};
