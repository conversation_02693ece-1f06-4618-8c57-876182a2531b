// 测试脚本：生成单张插画
// 用于测试LIBLIB API连接和插画生成效果
// 使用方法：node testSingleIllustration.js

import dotenv from 'dotenv';

// 加载环境变量
dotenv.config();

// 动态导入LIBLIB服务
async function loadLiblibService() {
  try {
    const { default: liblibService } = await import('./src/services/liblibService.js');
    return liblibService;
  } catch (error) {
    console.error('❌ 无法加载LIBLIB服务:', error.message);
    throw error;
  }
}

// 测试用的插画提示词
const TEST_PROMPT = `
A cute brown bear (<PERSON><PERSON>) sitting in front of a small wooden house in the forest, looking curiously at distant trees and flowers. 

Children's watercolor illustration style, warm and friendly, designed for autistic children aged 6-8:
- Soft gentle colors with clear outlines
- Watercolor technique with consistent line thickness  
- Warm color palette: browns, greens, blues, yellows
- Simple, uncluttered background
- Clear emotional expressions

The bear has:
- Big curious eyes and round face
- Kind, gentle expression
- Brown fur with warm tones
- Sitting peacefully on a tree stump

Setting:
- Small cozy wooden house in background
- Tall trees and colorful wildflowers
- Morning sunlight filtering through trees
- Peaceful forest clearing

Mood: Peaceful, curious, slightly shy but hopeful
`;

// 主测试函数
async function testIllustrationGeneration() {
  console.log('🧪 开始测试LIBLIB AI插画生成');
  
  try {
    // 检查环境变量
    const accessKey = process.env.VITE_LIBLIB_ACCESS_KEY;
    const secretKey = process.env.VITE_LIBLIB_SECRET_KEY;
    
    if (!accessKey || !secretKey) {
      throw new Error('请在.env文件中设置LIBLIB API密钥：\nVITE_LIBLIB_ACCESS_KEY=your_access_key\nVITE_LIBLIB_SECRET_KEY=your_secret_key');
    }
    
    console.log('✅ API密钥配置检查通过');
    
    // 加载LIBLIB服务
    const liblibService = await loadLiblibService();
    console.log('✅ LIBLIB服务模块加载成功');
    
    // 初始化API密钥
    liblibService.initializeApiKeys(accessKey, secretKey);
    console.log('✅ LIBLIB API密钥初始化成功');
    
    // 检查API状态
    const apiStatus = liblibService.getApiStatus();
    console.log('📊 API状态:', apiStatus);
    
    // 生成测试插画
    console.log('\n🎨 开始生成测试插画...');
    console.log('📝 提示词长度:', TEST_PROMPT.length, '字符');
    console.log('⏳ 预计需要1-2分钟，请耐心等待...');
    
    const startTime = Date.now();
    
    try {
      const imageUrl = await liblibService.generateImage(TEST_PROMPT, '6-8岁');
      
      const endTime = Date.now();
      const duration = Math.round((endTime - startTime) / 1000);
      
      console.log('\n🎉 测试插画生成成功！');
      console.log('🔗 图片URL:', imageUrl);
      console.log('⏱️ 生成耗时:', duration, '秒');
      
      console.log('\n💡 接下来您可以：');
      console.log('   1. 在浏览器中打开图片URL查看效果');
      console.log('   2. 如果效果满意，运行完整的插画生成脚本：npm run generate-illustrations');
      console.log('   3. 如果需要调整，可以修改提示词后重新测试');
      
      return {
        success: true,
        imageUrl: imageUrl,
        duration: duration
      };
      
    } catch (generateError) {
      console.error('\n❌ 插画生成失败:', generateError.message);
      
      console.log('\n🔍 可能的解决方案：');
      console.log('   1. 检查API密钥是否正确');
      console.log('   2. 检查网络连接');
      console.log('   3. 确认LIBLIB账户余额充足');
      console.log('   4. 稍后重试（可能是临时网络问题）');
      
      return {
        success: false,
        error: generateError.message
      };
    }
    
  } catch (error) {
    console.error('\n💥 测试过程中发生错误:', error.message);
    
    if (error.message.includes('API密钥')) {
      console.log('\n🔧 解决步骤：');
      console.log('   1. 确保项目根目录有.env文件');
      console.log('   2. 在.env文件中添加：');
      console.log('      VITE_LIBLIB_ACCESS_KEY=your_access_key_here');
      console.log('      VITE_LIBLIB_SECRET_KEY=your_secret_key_here');
      console.log('   3. 重新运行测试脚本');
    }
    
    return {
      success: false,
      error: error.message
    };
  }
}

// 运行测试
testIllustrationGeneration()
  .then(result => {
    if (result.success) {
      console.log('\n✨ 测试完成，LIBLIB AI集成正常工作！');
      process.exit(0);
    } else {
      console.log('\n⚠️ 测试未通过，请检查配置和网络连接。');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('\n💥 测试脚本执行失败:', error);
    process.exit(1);
  });
