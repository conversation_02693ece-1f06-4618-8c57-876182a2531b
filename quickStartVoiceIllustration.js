// 语音生成插画功能快速启动脚本
// 帮助用户快速配置和测试功能

import liblibService from './src/services/liblibService.js';
import { generateIllustrationFromAnswer } from './src/services/illustrationGenerator.js';

// 配置向导
async function configurationWizard() {
  console.log('🎨 语音生成插画功能配置向导');
  console.log('=====================================\n');

  // 检查API密钥状态
  const status = liblibService.getApiStatus();
  console.log('📊 当前状态:');
  console.log(`   API初始化: ${status.isInitialized ? '✅' : '❌'}`);
  console.log(`   AccessKey: ${status.hasAccessKey ? '✅' : '❌'}`);
  console.log(`   SecretKey: ${status.hasSecretKey ? '✅' : '❌'}`);
  console.log(`   基础URL: ${status.baseUrl}`);
  console.log(`   模板UUID: ${status.templateUuid}\n`);

  if (!status.isInitialized) {
    console.log('⚠️  API密钥未配置，请按照以下步骤配置：\n');
    console.log('1. 登录LIBLIB AI平台 (https://liblibai.com)');
    console.log('2. 获取您的AccessKey和SecretKey');
    console.log('3. 在代码中调用以下方法：');
    console.log('   liblibService.initializeApiKeys("YOUR_ACCESS_KEY", "YOUR_SECRET_KEY");\n');
    return false;
  }

  return true;
}

// 功能演示
async function demonstrateFeatures() {
  console.log('🎭 功能演示');
  console.log('=============\n');

  // 演示场景1：基础语音回答生成插画
  console.log('📝 场景1：基础语音回答生成插画');
  const scenario1 = {
    answer: '小熊波波和小兔莉莉在森林里开心地分享蛋糕',
    pageId: 4,
    context: {
      currentPageIndex: 3,
      currentPage: {
        id: 4,
        content: '小熊波波遇到了小兔莉莉，他们成为了好朋友。',
        isInteractive: true,
        question: '你觉得他们会一起做什么有趣的事情呢？'
      }
    },
    allImages: [
      { pageIndex: 0, url: '/page1.png' },
      { pageIndex: 1, url: '/page2.png' },
      { pageIndex: 2, url: '/page3.png' }
    ]
  };

  console.log(`   用户回答: "${scenario1.answer}"`);
  console.log(`   页面ID: ${scenario1.pageId}`);
  console.log('   正在生成插画...');

  try {
    const result1 = await generateIllustrationFromAnswer(
      scenario1.answer,
      scenario1.pageId,
      scenario1.context,
      scenario1.allImages
    );
    console.log(`   ✅ 生成成功: ${result1}\n`);
  } catch (error) {
    console.log(`   ❌ 生成失败: ${error.message}\n`);
  }

  // 演示场景2：情感丰富的回答
  console.log('📝 场景2：情感丰富的回答');
  const scenario2 = {
    answer: '我觉得小兔莉莉有点害怕，但是小熊波波很勇敢地帮助她，他们一起探索神秘的洞穴',
    pageId: 8,
    context: {
      currentPageIndex: 7,
      currentPage: {
        id: 8,
        content: '他们来到了一个神秘的洞穴前。',
        isInteractive: true,
        question: '你觉得他们会怎么面对这个挑战呢？'
      }
    },
    allImages: [
      { pageIndex: 4, url: '/page5.png' },
      { pageIndex: 5, url: '/page6.png' },
      { pageIndex: 6, url: '/page7.png' }
    ]
  };

  console.log(`   用户回答: "${scenario2.answer}"`);
  console.log(`   页面ID: ${scenario2.pageId}`);
  console.log('   正在生成插画...');

  try {
    const result2 = await generateIllustrationFromAnswer(
      scenario2.answer,
      scenario2.pageId,
      scenario2.context,
      scenario2.allImages
    );
    console.log(`   ✅ 生成成功: ${result2}\n`);
  } catch (error) {
    console.log(`   ❌ 生成失败: ${error.message}\n`);
  }
}

// 性能测试
async function performanceTest() {
  console.log('⚡ 性能测试');
  console.log('=============\n');

  const testCases = [
    '小熊波波在阳光下微笑',
    '小兔莉莉和松鼠兄弟一起玩耍',
    '乌龟老师在教室里读书'
  ];

  for (let i = 0; i < testCases.length; i++) {
    const testCase = testCases[i];
    console.log(`📊 测试用例 ${i + 1}: "${testCase}"`);
    
    const startTime = Date.now();
    try {
      const result = await generateIllustrationFromAnswer(
        testCase,
        i + 1,
        {
          currentPageIndex: i,
          currentPage: {
            id: i + 1,
            content: '测试页面内容',
            isInteractive: true,
            question: '测试问题'
          }
        },
        []
      );
      const endTime = Date.now();
      const duration = (endTime - startTime) / 1000;
      
      console.log(`   ✅ 生成成功，耗时: ${duration}秒`);
      console.log(`   🔗 结果: ${result}\n`);
    } catch (error) {
      const endTime = Date.now();
      const duration = (endTime - startTime) / 1000;
      
      console.log(`   ❌ 生成失败，耗时: ${duration}秒`);
      console.log(`   错误: ${error.message}\n`);
    }
  }
}

// 使用指南
function showUsageGuide() {
  console.log('📖 使用指南');
  console.log('=============\n');

  console.log('1. 在React组件中使用:');
  console.log('```javascript');
  console.log('import { generateIllustrationFromAnswer } from "./services/illustrationGenerator";');
  console.log('');
  console.log('// 在用户提交回答后调用');
  console.log('const imageUrl = await generateIllustrationFromAnswer(');
  console.log('  userResponse,    // 用户的语音回答文本');
  console.log('  pageId,          // 当前页面ID');
  console.log('  context,         // 故事上下文');
  console.log('  allImages        // 所有可用图像');
  console.log(');');
  console.log('```\n');

  console.log('2. 配置API密钥:');
  console.log('```javascript');
  console.log('import liblibService from "./services/liblibService";');
  console.log('');
  console.log('liblibService.initializeApiKeys(');
  console.log('  "YOUR_ACCESS_KEY",');
  console.log('  "YOUR_SECRET_KEY"');
  console.log(');');
  console.log('```\n');

  console.log('3. 错误处理:');
  console.log('```javascript');
  console.log('try {');
  console.log('  const imageUrl = await generateIllustrationFromAnswer(...);');
  console.log('  // 使用生成的图像URL');
  console.log('} catch (error) {');
  console.log('  console.error("插画生成失败:", error.message);');
  console.log('  // 显示错误信息给用户');
  console.log('}');
  console.log('```\n');
}

// 主函数
async function main() {
  console.log('🎨 语音生成插画功能快速启动');
  console.log('==============================\n');

  // 1. 配置检查
  const isConfigured = await configurationWizard();
  
  if (!isConfigured) {
    console.log('请先配置API密钥后再运行此脚本。');
    return;
  }

  // 2. 功能演示
  console.log('开始功能演示...\n');
  await demonstrateFeatures();

  // 3. 性能测试
  console.log('开始性能测试...\n');
  await performanceTest();

  // 4. 使用指南
  showUsageGuide();

  console.log('🎉 快速启动完成！');
  console.log('现在您可以在应用中使用语音生成插画功能了。');
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}

export { main as quickStart };
