// 添加Web Speech API的类型声明
declare global {
  interface Window {
    SpeechRecognition?: any;
    webkitSpeechRecognition?: any;
  }
}

import { useState, useRef, useEffect } from 'react';
import type { StoryPage as StoryPageType } from '../data/storyData';
import { Button } from './ui/button';
import { Textarea } from './ui/textarea';
import { Card } from './ui/card';

interface StoryPageProps {
  page: StoryPageType;
  onNext: () => void;
  onResponseSubmit: (pageId: number, response: string) => void;
  userResponses: Array<{pageId: number, response: string}>;
}

export function StoryPage({ page, onNext, onResponseSubmit, userResponses }: StoryPageProps) {
  const [userResponse, setUserResponse] = useState('');
  const [showGuidance, setShowGuidance] = useState(false);
  const [timeLeft, setTimeLeft] = useState(30);
  const [responseSubmitted, setResponseSubmitted] = useState(false);
  const [isListening, setIsListening] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  
  const recognitionRef = useRef<any>(null);
  const timerRef = useRef<number | null>(null);
  const timerStartedRef = useRef<boolean>(false);
  
  // 检查该页面是否已有回答
  useEffect(() => {
    const existingResponse = userResponses.find(r => r.pageId === page.id);
    if (existingResponse) {
      setUserResponse(existingResponse.response);
      setResponseSubmitted(true);
    } else {
      setUserResponse('');
      setResponseSubmitted(false);
    }
    
    // 重置状态
    setTimeLeft(30);
    setShowGuidance(false);
    timerStartedRef.current = false;
    
    // 停止之前的语音
    if ('speechSynthesis' in window) {
      window.speechSynthesis.cancel();
    }
    
    // 自动朗读内容
    const textToRead = page.isInteractive 
      ? `${page.content} ${page.interactiveQuestion}`
      : page.content;
    
    // 小延迟确保UI先渲染
    setTimeout(() => {
      speak(textToRead);
    }, 300);
    
    // 清理函数
    return () => {
      // 清理计时器
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
      
      // 停止语音识别
      if (recognitionRef.current) {
        try {
          recognitionRef.current.abort();
        } catch (e) {
          console.error('停止语音识别失败', e);
        }
      }
    };
  }, [page.id, userResponses]);
  
  // 初始化语音识别
  const initSpeechRecognition = () => {
    if ('SpeechRecognition' in window || 'webkitSpeechRecognition' in window) {
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      recognitionRef.current = new SpeechRecognition();
      recognitionRef.current.continuous = true;
      recognitionRef.current.interimResults = true;
      recognitionRef.current.lang = 'zh-CN';
      
      recognitionRef.current.onresult = (event: any) => {
        const transcript = Array.from(event.results)
          .map((result: any) => result[0])
          .map((result: any) => result.transcript)
          .join('');
        
        setUserResponse(transcript);
      };
      
      recognitionRef.current.onerror = (event: any) => {
        console.error('Speech recognition error', event.error);
        setIsListening(false);
      };
      
      recognitionRef.current.onend = () => {
        setIsListening(false);
      };
    }
  };
  
  // 文本转语音功能
  const speak = (text: string) => {
    if (!('speechSynthesis' in window)) {
      console.error('浏览器不支持语音合成');
      return;
    }
    
    const synth = window.speechSynthesis;
    
    // 确保停止任何正在进行的语音
    if (synth.speaking) {
      synth.cancel();
    }
    
    setIsSpeaking(true);
    
    const utterance = new SpeechSynthesisUtterance(text);
    utterance.lang = 'zh-CN';
    utterance.rate = 0.9;
    utterance.pitch = 1;
    
    utterance.onstart = () => {
      setIsSpeaking(true);
    };
    
    utterance.onend = () => {
      setIsSpeaking(false);
      
      // 如果是交互页面，开始计时器
      if (page.isInteractive && !responseSubmitted && !timerStartedRef.current) {
        startTimer();
      }
    };
    
    synth.speak(utterance);
  };
  
  // 开始计时器
  const startTimer = () => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }
    
    timerStartedRef.current = true;
    
    timerRef.current = window.setInterval(() => {
      setTimeLeft((prev) => {
        if (prev <= 1) {
          if (timerRef.current) {
            clearInterval(timerRef.current);
          }
          setShowGuidance(true);
          // 时间到时朗读引导提示
          if (page.guidancePrompt) {
            speak(page.guidancePrompt);
          }
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };
  
  // 初始化
  useEffect(() => {
    // 初始化语音识别
    initSpeechRecognition();
  }, []);
  
  const toggleListening = () => {
    if (!recognitionRef.current) {
      initSpeechRecognition();
    }
    
    if (isListening) {
      if (recognitionRef.current) {
        try {
          recognitionRef.current.stop();
        } catch (e) {
          console.error('停止语音识别失败', e);
        }
      }
      setIsListening(false);
    } else {
      if (recognitionRef.current) {
        try {
          recognitionRef.current.start();
          setIsListening(true);
        } catch (e) {
          console.error('启动语音识别失败', e);
          alert('启动语音识别失败，请确保您的浏览器支持此功能并已授予麦克风权限。');
        }
      } else {
        alert('您的浏览器不支持语音识别功能。');
      }
    }
  };
  
  const handleSubmit = () => {
    if (userResponse.trim()) {
      if (isListening && recognitionRef.current) {
        try {
          recognitionRef.current.stop();
        } catch (e) {
          console.error('停止语音识别失败', e);
        }
      }
      
      onResponseSubmit(page.id, userResponse);
      setResponseSubmitted(true);
      speak("谢谢你的回答！");
      
      setTimeout(onNext, 2500); // 给用户时间看到确认信息再继续
    }
  };
  
  const readAgain = () => {
    const textToRead = page.isInteractive 
      ? `${page.content} ${page.interactiveQuestion}`
      : page.content;
    speak(textToRead);
  };

  return (
    <div className="flex flex-col items-center max-w-4xl mx-auto p-4">
      <h2 className="text-2xl font-bold mb-4">第{page.id}页{page.isInteractive ? ' - 交互环节' : ''}</h2>
      
      <div className="w-full mb-6">
        <img 
          src={page.imagePath} 
          alt={`故事第${page.id}页插图`} 
          className="w-full max-h-[400px] object-contain rounded-lg shadow-md"
        />
      </div>
      
      <Card className="w-full p-4 mb-6">
        <div className="flex justify-end mb-2">
          <Button 
            variant="outline" 
            size="sm" 
            onClick={readAgain}
            disabled={isSpeaking}
          >
            {isSpeaking ? '正在朗读...' : '朗读内容'}
          </Button>
        </div>
        
        <p className="text-lg mb-4">{page.content}</p>
        
        {page.isInteractive && (
          <div className="mt-4 border-t pt-4">
            <h3 className="text-xl font-semibold mb-2">交互问题：</h3>
            <p className="mb-4">{page.interactiveQuestion}</p>
            
            {showGuidance && !responseSubmitted && (
              <div className="bg-amber-50 border border-amber-200 p-3 rounded-md mb-4">
                <p className="text-amber-800">{page.guidancePrompt}</p>
              </div>
            )}
            
            {!responseSubmitted ? (
              <>
                <div className="flex items-center gap-2 mb-2">
                  <Button 
                    variant={isListening ? "destructive" : "default"}
                    onClick={toggleListening}
                  >
                    {isListening ? '停止录音' : '开始语音输入'}
                  </Button>
                  <span className="text-sm text-gray-500">
                    {isListening && '正在聆听...请说话'}
                  </span>
                </div>
                
                <Textarea
                  value={userResponse}
                  onChange={(e) => setUserResponse(e.target.value)}
                  placeholder="请在这里输入你的回答，或使用语音输入..."
                  className="min-h-[120px] mb-2"
                />
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-500">
                    {timeLeft > 0 && `剩余时间: ${timeLeft}秒`}
                    {timeLeft === 0 && "时间到"}
                  </span>
                  <Button onClick={handleSubmit} disabled={!userResponse.trim()}>
                    提交回答
                  </Button>
                </div>
              </>
            ) : (
              <div className="bg-green-50 border border-green-200 p-3 rounded-md">
                <p className="text-green-800">谢谢你的回答！</p>
              </div>
            )}
          </div>
        )}
        
        {!page.isInteractive && (
          <div className="flex justify-end mt-4">
            <Button onClick={onNext}>继续阅读</Button>
          </div>
        )}
      </Card>
    </div>
  );
}
